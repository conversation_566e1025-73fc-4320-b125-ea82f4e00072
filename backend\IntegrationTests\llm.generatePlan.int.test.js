/**
 * Integration Test for /generatePlan endpoint of LLMServiceV3
 * This test makes a real call through the stack with no mocks.
 * Place in: D:/repos-personal/repos/JustProtoType/backend/IntegrationTests/llm.generatePlan.int.test.js
 */

const request = require('supertest');
const app = require('../../server'); // Adjust the path if your express app is elsewhere
const session = require('supertest-session');

describe('/api/llm/v3/generatePlan E2E', () => {
  let userSession;

  beforeAll(async () => {
    // Replace with real login or session bootstrapping if required by your app
    userSession = session(app);
    await userSession
      .post('/login')
      .send({ username: process.env.TEST_USER || 'demo', password: process.env.TEST_PASS || 'demo' });
  });

  it('should return a valid plan object for a real prompt', async () => {
    const res = await userSession
      .post('/api/llm/v3/generatePlan')
      .send({ prompt: 'Dashboard UI', deviceType: 'desktop' });

    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('plan');
    expect(res.body.plan).toHaveProperty('overview');
    expect(res.body.plan).toHaveProperty('sections');
    expect(Array.isArray(res.body.plan.sections)).toBe(true);
  });

  it('should return 400 if prompt is missing', async () => {
    const res = await userSession
      .post('/api/llm/v3/generatePlan')
      .send({ deviceType: 'desktop' });

    expect(res.status).toBe(400);
  });

  it('should return 401 if session is missing', async () => {
    const res = await request(app)
      .post('/api/llm/v3/generatePlan')
      .send({ prompt: 'Dashboard UI', deviceType: 'desktop' });

    expect(res.status).toBe(401);
  });
});